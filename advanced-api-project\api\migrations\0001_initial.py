# Generated by Django 5.2.4 on 2025-08-09 03:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Author',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='The full name of the author', max_length=100)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Book',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='The title of the book', max_length=200)),
                ('publication_year', models.IntegerField(help_text='The year the book was published')),
                ('author', models.ForeignKey(help_text='The author who wrote this book', on_delete=django.db.models.deletion.CASCADE, related_name='books', to='api.author')),
            ],
            options={
                'ordering': ['-publication_year', 'title'],
            },
        ),
    ]
