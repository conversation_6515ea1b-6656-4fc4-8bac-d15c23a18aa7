# Generated by Django 5.2.4 on 2025-08-05 09:10

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Book',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=200)),
                ('author', models.Char<PERSON>ield(max_length=100)),
                ('publication_year', models.IntegerField()),
            ],
        ),
    ]
